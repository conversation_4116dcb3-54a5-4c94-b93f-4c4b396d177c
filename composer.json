{"name": "pfg/log-reader-module", "description": "Comprehensive log management module for Magento 1.9 with secure file operations and admin interface", "type": "magento-module", "version": "1.0.0", "license": "proprietary", "authors": [{"name": "PFG Development Team", "email": "<EMAIL>"}], "keywords": ["magento", "magento1", "log-reader", "log-management", "admin-tools", "debugging", "exception-handling"], "require": {"php": ">=5.6.0", "magento-hackathon/magento-composer-installer": "*"}, "suggest": {"ext-redis": "For rate limiting functionality"}, "support": {"source": "https://bitbucket.org/pfg/log-reader-module", "issues": "https://bitbucket.org/pfg/log-reader-module/issues"}, "extra": {"magento-root-dir": "./", "magento-deploystrategy": "copy", "magento-force": true}}