# Installation Guide - PFG Log Reader Module

This guide provides detailed instructions for installing the PFG Log Reader module on Magento 1.9.

## Prerequisites

### System Requirements
- **Magento Version**: 1.9.x (tested on 1.9.4.5)
- **PHP Version**: 5.6+ (recommended: PHP 7.4)
- **Memory Limit**: Minimum 256MB (recommended: 512MB+)
- **File Permissions**: Read/write access to Magento directories

### Optional Requirements
- **Redis**: For rate limiting functionality (recommended for production)
- **Composer**: For dependency management (optional)

## Installation Methods

### Method 1: Manual Installation (Recommended)

#### Step 1: Download the Module
```bash
# Clone from repository
git clone https://bitbucket.org/pfg/log-reader-module.git pfg-log-reader

# Or download and extract ZIP file
wget https://bitbucket.org/pfg/log-reader-module/archive/master.zip
unzip master.zip
```

#### Step 2: Copy Files to Magento
```bash
# Navigate to your Magento root directory
cd /path/to/your/magento/

# Copy module files
cp -r pfg-log-reader/app/* app/

# Verify file structure
ls -la app/code/local/PFG/LogReader/
ls -la app/etc/modules/PFG_LogReader.xml
```

#### Step 3: Set Proper Permissions
```bash
# Set ownership (adjust user:group as needed)
chown -R www-data:www-data app/code/local/PFG/
chown www-data:www-data app/etc/modules/PFG_LogReader.xml

# Set permissions
chmod -R 755 app/code/local/PFG/
chmod 644 app/etc/modules/PFG_LogReader.xml
```

#### Step 4: Clear Cache
```bash
# Clear Magento cache
rm -rf var/cache/*
rm -rf var/full_page_cache/*

# Or use Magento admin: System > Cache Management > Flush All Cache
```

### Method 2: Modman Installation

#### Step 1: Install Modman (if not already installed)
```bash
bash < <(curl -s -L https://raw.github.com/colinmollenhour/modman/master/modman-installer)
```

#### Step 2: Initialize Modman in Magento Root
```bash
cd /path/to/your/magento/
modman init
```

#### Step 3: Install Module via Modman
```bash
modman clone https://bitbucket.org/pfg/log-reader-module.git
```

## Post-Installation Configuration

### Step 1: Verify Installation
1. Login to Magento Admin Panel
2. Navigate to **System > Configuration**
3. Look for **PFG** section in the left sidebar
4. Click on **Log Reader** under PFG section

### Step 2: Enable the Module
1. In the **General Settings** section:
   - Set **Enable Log Reader** to "Yes"
   - Configure **Max File Size** (default: 10MB)
   - Set **Lines to Display** (default: 50)

### Step 3: Configure Security Settings (Optional)
1. **Rate Limiting**:
   - Enable if Redis is available
   - Set **Max Requests per Minute** (default: 60)
   
2. **File Access**:
   - Verify log directory permissions
   - Test file reading functionality

### Step 4: Test the Installation
1. Save configuration settings
2. The Log Reader interface should appear below the configuration
3. Test both **Log Files** and **Exception Files** tabs
4. Verify file listing and content viewing works

## Directory Structure Verification

After installation, verify the following directory structure exists:

```
app/
├── code/local/PFG/LogReader/
│   ├── Block/
│   │   └── Adminhtml/System/Config/Form/Fieldset/
│   │       └── Logviewer.php
│   ├── controllers/
│   │   └── Adminhtml/Pfg/
│   │       └── LogreaderController.php
│   ├── etc/
│   │   ├── config.xml
│   │   └── system.xml
│   ├── Helper/
│   │   └── Data.php
│   └── Model/
│       ├── File.php
│       └── Security.php
└── etc/modules/
    └── PFG_LogReader.xml
```

## Troubleshooting Installation Issues

### Issue: Module Not Appearing in Admin

**Symptoms**: PFG section not visible in System > Configuration

**Solutions**:
1. Clear all caches: `rm -rf var/cache/*`
2. Check file permissions on `app/etc/modules/PFG_LogReader.xml`
3. Verify XML syntax in module declaration file
4. Check web server error logs

### Issue: Permission Denied Errors

**Symptoms**: Cannot read log files or access directories

**Solutions**:
1. Set proper file permissions:
   ```bash
   chmod -R 755 app/code/local/PFG/
   chmod 644 app/etc/modules/PFG_LogReader.xml
   ```
2. Ensure web server can read `var/log/` and `var/report/` directories
3. Check SELinux settings (if applicable)

### Issue: AJAX Requests Failing

**Symptoms**: File content not loading, JavaScript errors

**Solutions**:
1. Check browser console for JavaScript errors
2. Verify admin session is active
3. Check `var/log/pfg_logreader.log` for errors
4. Ensure CSRF form keys are working

### Issue: Rate Limiting Not Working

**Symptoms**: Rate limiting configuration has no effect

**Solutions**:
1. Verify Redis is installed and running
2. Check Redis connection in Magento configuration
3. Test Redis connectivity: `redis-cli ping`
4. Check Redis logs for connection errors

## Performance Optimization

### For Large Log Directories
1. Increase PHP memory limit: `memory_limit = 512M`
2. Adjust pagination settings in module configuration
3. Consider log rotation to manage file sizes
4. Enable Redis caching for better performance

### For High-Traffic Sites
1. Enable rate limiting with Redis
2. Set appropriate cache TTL values
3. Monitor server resources during log operations
4. Consider dedicated log management server

## Security Considerations

### File Permissions
- Ensure log directories are not web-accessible
- Set restrictive permissions on sensitive log files
- Regular audit of file access permissions

### Admin Access
- Use strong admin passwords
- Enable two-factor authentication if available
- Regularly review admin user permissions
- Monitor admin access logs

### Rate Limiting
- Enable Redis-based rate limiting in production
- Set appropriate request limits
- Monitor for suspicious access patterns
- Configure IP-based blocking if needed

## Uninstallation

### To Remove the Module:
1. **Disable in Admin**: Set "Enable Log Reader" to "No"
2. **Remove Files**:
   ```bash
   rm -rf app/code/local/PFG/LogReader/
   rm app/etc/modules/PFG_LogReader.xml
   ```
3. **Clear Cache**: `rm -rf var/cache/*`

### Clean Uninstall with Modman:
```bash
modman remove log-reader-module
```

## Support and Maintenance

### Regular Maintenance
- Monitor log file sizes and implement rotation
- Review error logs in `var/log/pfg_logreader.log`
- Update module when new versions are available
- Test functionality after Magento updates

### Getting Help
- Check the troubleshooting section above
- Review module logs for error details
- Contact PFG Development Team for support
- Submit issues via Bitbucket repository

---

**Installation Complete!** 

The PFG Log Reader module should now be fully functional. Access it through **System > Configuration > PFG > Log Reader** in your Magento admin panel.
