<?php
/**
 * PFG Log Reader Helper
 *
 * @category   PFG
 * @package    PFG_LogReader
 * <AUTHOR> Development Team
 */

/**
 * Log Reader Data Helper
 *
 * Provides utility methods for log file operations, configuration access,
 * and error logging functionality.
 *
 * @category   PFG
 * @package    PFG_LogReader
 * <AUTHOR> Development Team
 * @version    1.0.0
 * @since      1.0.0
 */
class PFG_LogReader_Helper_Data extends Mage_Core_Helper_Abstract
{
    /**
     * Log file name for this module
     */
    const LOG_FILE = 'pfg_logreader.log';

    /**
     * Configuration paths
     */
    const XML_PATH_ENABLED = 'pfg_logreader/general/enabled';
    const XML_PATH_FILES_PER_PAGE = 'pfg_logreader/general/files_per_page';
    const XML_PATH_LINES_TO_SHOW = 'pfg_logreader/general/lines_to_show';
    const XML_PATH_MAX_FILE_SIZE = 'pfg_logreader/general/max_file_size';

    /**
     * Check if module is enabled
     *
     * @return bool
     */
    public function isEnabled()
    {
        return Mage::getStoreConfigFlag(self::XML_PATH_ENABLED);
    }

    /**
     * Get number of files to display per page
     *
     * @return int
     */
    public function getFilesPerPage()
    {
        return (int) Mage::getStoreConfig(self::XML_PATH_FILES_PER_PAGE) ?: 20;
    }

    /**
     * Get number of lines to show from file
     *
     * @return int
     */
    public function getLinesToShow()
    {
        return (int) Mage::getStoreConfig(self::XML_PATH_LINES_TO_SHOW) ?: 50;
    }

    /**
     * Get maximum file size to read
     *
     * @return int
     */
    public function getMaxFileSize()
    {
        return (int) Mage::getStoreConfig(self::XML_PATH_MAX_FILE_SIZE) ?: 10485760; // 10MB
    }

    /**
     * Get log directory path
     *
     * @return string
     */
    public function getLogDirectory()
    {
        return Mage::getBaseDir('var') . DS . 'log';
    }

    /**
     * Get report directory path
     *
     * @return string
     */
    public function getReportDirectory()
    {
        return Mage::getBaseDir('var') . DS . 'report';
    }

    /**
     * Log message to module log file
     *
     * @param string $message
     * @param int $level
     * @param bool $forceLog
     * @return $this
     */
    public function log($message, $level = null, $forceLog = false)
    {
        Mage::log($message, $level, self::LOG_FILE, $forceLog);
        return $this;
    }

    /**
     * Log error message
     *
     * @param string $message
     * @param Exception $exception
     * @return $this
     */
    public function logError($message, $exception = null)
    {
        $logMessage = $message;
        if ($exception) {
            $logMessage .= ' - Exception: ' . $exception->getMessage();
            $logMessage .= ' - Trace: ' . $exception->getTraceAsString();
        }
        $this->log($logMessage, Zend_Log::ERR, true);
        return $this;
    }

    /**
     * Format file size for display
     *
     * @param int $bytes
     * @return string
     */
    public function formatFileSize($bytes)
    {
        if ($bytes >= 1073741824) {
            return number_format($bytes / 1073741824, 2) . ' GB';
        } elseif ($bytes >= 1048576) {
            return number_format($bytes / 1048576, 2) . ' MB';
        } elseif ($bytes >= 1024) {
            return number_format($bytes / 1024, 2) . ' KB';
        } else {
            return $bytes . ' bytes';
        }
    }

    /**
     * Check if file is readable and within size limits
     *
     * @param string $filePath
     * @return bool
     */
    public function isFileReadable($filePath)
    {
        if (!file_exists($filePath) || !is_readable($filePath)) {
            return false;
        }

        $fileSize = filesize($filePath);
        if ($fileSize === false || $fileSize > $this->getMaxFileSize()) {
            return false;
        }

        return true;
    }
}
